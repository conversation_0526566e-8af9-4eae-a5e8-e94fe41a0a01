'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import AssessmentLoadingPage from '../../components/assessment/AssessmentLoadingPage';
import { useAssessmentWorkflow } from '../../hooks/useAssessmentWorkflow';
import { useAssessmentWebSocket } from '../../hooks/useAssessmentWebSocket';
import { WorkflowState } from '../../utils/assessment-workflow';

export default function AssessmentLoadingPageRoute() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isLoading: authLoading, token } = useAuth();
  const [answers, setAnswers] = useState<Record<number, number | null> | null>(null);
  const [assessmentName, setAssessmentName] = useState<string>('AI-Driven Talent Mapping');

  // WebSocket for real-time updates with faster fallback
  const webSocket = useAssessmentWebSocket({
    autoConnect: true,
    fallbackToPolling: true, // Enable automatic fallback
    onConnected: () => {
      console.log('Assessment Loading: WebSocket connected successfully');
    },
    onDisconnected: () => {
      console.log('Assessment Loading: WebSocket disconnected');
    },
    onError: (error) => {
      console.warn('Assessment Loading: WebSocket error, will fallback to polling', error);
    },
  });

  // Get assessment workflow with optimized WebSocket preference
  const {
    state,
    isIdle,
    isProcessing,
    isCompleted,
    isFailed,
    result,
    submitFromAnswers,
    cancel,
    reset
  } = useAssessmentWorkflow({
    preferWebSocket: webSocket.isConnected, // Use WebSocket only if connected
    onComplete: (result) => {
      console.log('Assessment completed successfully:', result);
      // Faster redirect for better UX
      const delay = webSocket.isConnected ? 500 : 1000; // Reduced delays
      setTimeout(() => {
        router.push(`/results/${result.id}`);
      }, delay);
    },
    onError: (error) => {
      console.error('Assessment failed:', error);
      // Better error handling - show user-friendly message
    },
    onTokenBalanceUpdate: async () => {
      console.log('Token balance updated via WebSocket');
    }
  });

  // Load answers from localStorage or URL params on mount
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth');
      return;
    }

    // Try to get answers from localStorage first
    const savedAnswers = localStorage.getItem('assessment-answers');
    if (savedAnswers) {
      try {
        const parsedAnswers = JSON.parse(savedAnswers);
        setAnswers(parsedAnswers);
        
        // Get assessment name from localStorage if available
        const savedAssessmentName = localStorage.getItem('assessment-name');
        if (savedAssessmentName) {
          setAssessmentName(savedAssessmentName);
        }
      } catch (error) {
        console.error('Error parsing saved answers:', error);
      }
    }

    // Check URL params for answers (fallback)
    const answersParam = searchParams.get('answers');
    const nameParam = searchParams.get('name');
    
    if (answersParam && !savedAnswers) {
      try {
        const parsedAnswers = JSON.parse(decodeURIComponent(answersParam));
        setAnswers(parsedAnswers);
      } catch (error) {
        console.error('Error parsing answers from URL:', error);
      }
    }

    if (nameParam) {
      setAssessmentName(nameParam);
    }
  }, [authLoading, isAuthenticated, router, searchParams]);

  // Auto-submit when answers are loaded and workflow is idle
  useEffect(() => {
    if (answers && isIdle && !isProcessing && !isCompleted && !isFailed) {
      console.log('Auto-submitting assessment with answers:', answers);
      submitFromAnswers(answers, assessmentName);
    }
  }, [answers, isIdle, isProcessing, isCompleted, isFailed, submitFromAnswers, assessmentName]);

  // Handle cancel
  const handleCancel = () => {
    cancel();
    // Clear saved data
    localStorage.removeItem('assessment-answers');
    localStorage.removeItem('assessment-name');
    router.push('/assessment');
  };

  // Handle retry
  const handleRetry = () => {
    reset();
    if (answers) {
      setTimeout(() => {
        submitFromAnswers(answers, assessmentName);
      }, 500);
    }
  };

  // Handle back to assessment (for failed state)
  const handleBackToAssessment = () => {
    // Clear saved data
    localStorage.removeItem('assessment-answers');
    localStorage.removeItem('assessment-name');
    router.push('/assessment');
  };

  // Show loading while checking auth or loading answers
  if (authLoading || (!answers && !isFailed)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600">
            {authLoading ? 'Memverifikasi autentikasi...' : 'Memuat data assessment...'}
          </p>
          {webSocket.isConnected && (
            <p className="text-sm text-green-600">✓ WebSocket terhubung</p>
          )}
          {webSocket.connectionError && (
            <p className="text-sm text-yellow-600">⚠ Menggunakan mode fallback</p>
          )}
        </div>
      </div>
    );
  }

  // If no answers found, redirect to assessment
  if (!answers && !isProcessing && !isCompleted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center space-y-4 max-w-md">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">
            Data Assessment Tidak Ditemukan
          </h2>
          <p className="text-gray-600">
            Sepertinya Anda belum menyelesaikan assessment atau data telah hilang. 
            Silakan kembali ke halaman assessment untuk memulai.
          </p>
          <button
            onClick={handleBackToAssessment}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Kembali ke Assessment
          </button>
        </div>
      </div>
    );
  }

  return (
    <AssessmentLoadingPage
      workflowState={state}
      onCancel={isFailed ? handleBackToAssessment : handleCancel}
      onRetry={isFailed ? handleRetry : undefined}
    />
  );
}
